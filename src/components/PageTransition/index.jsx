"use client";

import { useEffect, useRef, useCallback } from "react";
import { useRouter, usePathname } from "next/navigation";
import { gsap } from "gsap";

const PageTransition = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const currentPageRef = useRef(null);
  const isTransitioning = useRef(false);
  const revealTimeoutRef = useRef(null);

  const slidePageUp = useCallback((url) => {
    if (!currentPageRef.current) return;

    // Animation de la page actuelle qui glisse vers le haut avec effet de carte
    gsap.to(currentPageRef.current, {
      y: "-100vh",
      scale: 0.95,
      rotationX: 5,
      duration: 0.9,
      ease: "power3.inOut",
      transformOrigin: "center bottom",
      onComplete: () => {
        // Navigation vers la nouvelle page
        router.push(url);
      },
    });
  }, [router]);

  const handleRouteChange = useCallback((url) => {
    if (isTransitioning.current) return;
    isTransitioning.current = true;
    slidePageUp(url);
  }, [slidePageUp]);

  const onAnchorClick = useCallback(
    (e) => {
      if (isTransitioning.current) {
        e.preventDefault();
        return;
      }

      if (
        e.metaKey ||
        e.ctrlKey ||
        e.shiftKey ||
        e.altKey ||
        e.button !== 0 ||
        e.currentTarget.target === "_blank"
      ) {
        return;
      }

      e.preventDefault();
      const href = e.currentTarget.href;
      const url = new URL(href).pathname;
      if (url !== pathname) {
        handleRouteChange(url);
      }
    },
    [pathname, handleRouteChange]
  );

  const revealPage = useCallback(() => {
    if (revealTimeoutRef.current) {
      clearTimeout(revealTimeoutRef.current);
    }

    // Animation de révélation de la nouvelle page (slide down from top)
    if (currentPageRef.current) {
      // Vérifier si on vient d'une transition (page était en haut)
      const currentY = gsap.getProperty(currentPageRef.current, "y");

      if (currentY === "-100vh" || currentY === -window.innerHeight) {
        // On vient d'une transition, animer depuis le haut
        gsap.fromTo(currentPageRef.current,
          {
            y: "-100vh",
            scale: 0.95,
            rotationX: 5
          },
          {
            y: 0,
            scale: 1,
            rotationX: 0,
            duration: 0.9,
            ease: "power3.inOut",
            transformOrigin: "center bottom",
            onComplete: () => {
              isTransitioning.current = false;
            },
          }
        );
      } else {
        // Chargement initial de la page, pas d'animation
        gsap.set(currentPageRef.current, {
          y: 0,
          scale: 1,
          rotationX: 0,
          transformOrigin: "center bottom"
        });
        isTransitioning.current = false;
      }
    }
  }, []);

  useEffect(() => {
    // Initialiser les pages
    if (currentPageRef.current) {
      gsap.set(currentPageRef.current, {
        y: 0,
        scale: 1,
        rotationX: 0,
        transformOrigin: "center bottom"
      });
    }

    revealPage();

    // Handle internal links with locale support
    const links = document.querySelectorAll('a[href^="/"]');
    links.forEach((link) => {
      link.addEventListener("click", onAnchorClick);
    });

    return () => {
      links.forEach((link) => {
        link.removeEventListener("click", onAnchorClick);
      });
      if (revealTimeoutRef.current) {
        clearTimeout(revealTimeoutRef.current);
      }
    };
  }, [router, pathname, onAnchorClick, revealPage]);



  return (
    <div ref={currentPageRef} className="page-transition-current">
      {children}
    </div>
  );
};

export default PageTransition;
